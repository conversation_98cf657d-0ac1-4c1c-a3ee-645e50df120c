import React from "react";
import { FaF<PERSON>book<PERSON>, FaTwi<PERSON>, FaInstagram, FaLinkedinIn } from "react-icons/fa";

const HomePage = () => {
  return (
    <div
      style={{
        display: "flex",
        minHeight: "100vh",
        backgroundColor: "#1a3d2e",
        paddingLeft: "20px",
        paddingRight: "20px",
        paddingBottom: "80px",
      }}
    >
      <div style={{width: '10%'}}>
        <div>2023 Dotcreativemarket</div>
        <div>
          <div>FOLLOW US</div>
          <div style={{ display: 'flex', gap: '15px', marginTop: '10px' }}>
            <FaFacebookF
              style={{
                color: '#ffffff',
                fontSize: '20px',
                cursor: 'pointer',
                transition: 'color 0.3s ease'
              }}
              onMouseEnter={(e) => e.target.style.color = '#4267B2'}
              onMouseLeave={(e) => e.target.style.color = '#ffffff'}
            />
            <FaTwitter
              style={{
                color: '#ffffff',
                fontSize: '20px',
                cursor: 'pointer',
                transition: 'color 0.3s ease'
              }}
              onMouseEnter={(e) => e.target.style.color = '#1DA1F2'}
              onMouseLeave={(e) => e.target.style.color = '#ffffff'}
            />
            <FaInstagram
              style={{
                color: '#ffffff',
                fontSize: '20px',
                cursor: 'pointer',
                transition: 'color 0.3s ease'
              }}
              onMouseEnter={(e) => e.target.style.color = '#E4405F'}
              onMouseLeave={(e) => e.target.style.color = '#ffffff'}
            />
            <FaLinkedinIn
              style={{
                color: '#ffffff',
                fontSize: '20px',
                cursor: 'pointer',
                transition: 'color 0.3s ease'
              }}
              onMouseEnter={(e) => e.target.style.color = '#0077B5'}
              onMouseLeave={(e) => e.target.style.color = '#ffffff'}
            />
          </div>
        </div>
      </div>
      <div style={{width: '40%'}}></div>
      <div style={{width: '50%'}}></div>
    </div>
  );
};

export default HomePage;
